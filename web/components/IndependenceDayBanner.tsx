'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';

interface IndependenceDayBannerProps {
  className?: string;
}

export default function IndependenceDayBanner({ className = '' }: IndependenceDayBannerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const auth = useAuth();
  const { user } = auth;
  
  useEffect(() => {
    // Only show banner for authenticated users
    if (user) {
      // Check if banner was dismissed in localStorage
      const dismissed = localStorage.getItem('independence-day-banner-dismissed');
      if (!dismissed) {
        setIsVisible(true);
      }
    } else {
      setIsVisible(false);
    }
  }, [user]);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('independence-day-banner-dismissed', 'true');
  };

  const handleBuyTokens = () => {
    window.open('/buy-tokens', '_blank');
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className={`sticky top-[4.5rem] z-10 bg-gradient-to-r from-red-600 to-red-700 text-white py-2 sm:py-3 px-4 shadow-lg ${className}`}>
      <div className="max-w-7xl mx-auto flex items-center justify-between gap-3">
        <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
          <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
            <div className="relative">
              <span className="text-yellow-300 text-sm sm:text-base">🇮🇩</span>
            </div>
            <span className="font-bold text-xs sm:text-sm md:text-base">🎉</span>
          </div>
          <div className="flex flex-col min-w-0">
            <span className="font-bold text-xs sm:text-sm md:text-base leading-tight">
              PROMO KEMERDEKAAN RI KE-80!
            </span>
            <span className="text-xs sm:text-sm opacity-90 leading-tight">
              Bonus token ekstra untuk kemerdekaan Indonesia
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={handleBuyTokens}
            className="bg-white text-red-600 hover:bg-yellow-100 font-bold py-1.5 px-3 sm:py-2 sm:px-4 rounded-full text-xs sm:text-sm text-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex-shrink-0"
          >
            Dapatkan Sekarang
          </button>
          <button
            onClick={handleDismiss}
            className="text-white hover:text-yellow-200 p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors flex-shrink-0"
            aria-label="Tutup banner"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}