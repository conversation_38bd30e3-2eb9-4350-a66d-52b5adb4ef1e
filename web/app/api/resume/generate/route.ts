export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createApiHandler } from "@/utils/apiErrorHandler";
import { captureApiError } from "@/utils/errorMonitoring";
import { trackApiUsage } from "@/lib/mixpanel-server";
import { createClient } from "@/lib/supabase-server";
import { ResumeInput, JobInput } from "@/types/resume";
import { EdgeFunctionRequest } from "@/utils/edgeFunction";
import { v4 as uuidv4 } from "uuid";
import mammoth from "mammoth";

/**
 * Process file data based on file type
 */
async function processFileData(buffer: ArrayBuffer, mimeType: string) {
  const supportedTypes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'image/png',
    'image/jpeg',
    'image/jpg'
  ];
  
  if (!supportedTypes.includes(mimeType)) {
    throw new Error('Unsupported file format. Please use PDF, DOCX, PNG, JPG, or JPEG files.');
  }

  if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    // Extract text from DOCX
    try {
      const nodeBuffer = Buffer.from(buffer);
      const result = await mammoth.extractRawText({ buffer: nodeBuffer });
      return { extractedText: result.value, mimeType };
    } catch (error) {
      console.error('Error extracting text from DOCX:', error);
      throw new Error('Failed to extract text from DOCX file');
    }
  } else {
    // For other file types (PDF, TXT, images), keep as buffer
    return { buffer, mimeType };
  }
}

async function handlePostRequest(req: NextRequest) {
  const startTime = Date.now();
  let userId: string | undefined;

  try {
    const formData = await req.formData();

    // Extract form data
    const resumeInputMethod = formData.get('resumeInputMethod') as string; // 'upload' or 'scratch'
    const jobInputMethod = formData.get('jobInputMethod') as string; // 'text' or 'image'
    const templateId = formData.get('templateId') as string; // template ID for resume generation

    // Resume inputs
    const resumeFile = formData.get('resumeFile') as File | null;
    const unauthenticatedResumeFile = formData.get('unauthenticatedResumeFile') as File | null;

    // Manual resume data
    const fullName = formData.get('fullName') as string | null;
    const professionalTitle = formData.get('professionalTitle') as string | null;
    const professionalSummary = formData.get('professionalSummary') as string | null;
    const jobTitle = formData.get('jobTitle') as string | null;
    const company = formData.get('company') as string | null;
    const achievements = formData.get('achievements') as string | null;
    const skills = formData.get('skills') as string | null;

    // Job inputs
    const jobDescription = formData.get('jobDescription') as string | null;
    const jobImage = formData.get('jobImage') as File | null;

    // Get access token for authenticated users
    const accessToken = req.headers.get('x-supabase-auth');

    if (!accessToken) {
      return NextResponse.json({ error: 'Missing authentication token' }, { status: 401 });
    }

    // Create Supabase client
    const supabase = await createClient();
        
    // Get user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return NextResponse.json({
        error: 'Anda harus login untuk menggunakan fitur ini'
      }, { status: 401 });
    }
    userId = user.id;

    // Get resume file path and token balance from `profiles` table
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('resume_file_name, tokens')
      .eq('id', userId)
      .single();

    if (profileError || !profile) {
      return NextResponse.json({
        error: 'Error fetching profile data'
      }, { status: 404 });
    }

    // Check token balance for AI-powered resume generation (25 tokens required)
    const requiredTokens = 25;
    const currentTokens = (profile.tokens as number | null) ?? 0;

    if (currentTokens < requiredTokens) {
      return NextResponse.json({
        error: `Token Anda tidak cukup untuk membuat CV`
      }, { status: 402 }); // 402 Payment Required
    }

    // Prepare resume input
    const resumeInput: ResumeInput = {};
    // If resume input comes from a file we store its filename for DB
    let resumeFileName: string | null = null;

    if (resumeInputMethod === 'upload') {
      let fileToUse: File | null = null;

      if (unauthenticatedResumeFile) {
        fileToUse = unauthenticatedResumeFile;
        resumeFileName = fileToUse.name;
      } else if (resumeFile) {
        fileToUse = resumeFile;
        resumeFileName = fileToUse.name;
      } else {
        if (!profile.resume_file_name) {
          return NextResponse.json({
            error: 'No resume file found in your profile. Please upload a resume first.'
          }, { status: 404 });
        }

        // Store original filename (for DB record) but use full path for download
        resumeFileName = profile.resume_file_name;

        // Download the resume file from storage using the stored path
        const { data: resumeData, error: downloadError } = await supabase.storage
          .from('resumes')
          .download(profile.resume_file_name);

        if (downloadError || !resumeData) {
          return NextResponse.json({
            error: 'Tidak dapat mengakses file resume. Harap unggah ulang resume Anda.'
          }, { status: 500 });
        }

        // Convert blob to file-like object
        const buffer = await resumeData.arrayBuffer();
        // Determine correct MIME type based on file extension (fallback to text/plain)
        const lcFileName = (resumeFileName || '').toLowerCase();
        let mimeType: string;
        if (lcFileName.endsWith('.pdf')) {
          mimeType = 'application/pdf';
        } else if (lcFileName.endsWith('.docx')) {
          mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        } else if (lcFileName.endsWith('.png')) {
          mimeType = 'image/png';
        } else if (lcFileName.endsWith('.jpg') || lcFileName.endsWith('.jpeg')) {
          mimeType = 'image/jpeg';
        } else {
          mimeType = 'text/plain';
        }
        resumeInput.file = {
          buffer,
          mimeType
        };
      }

      if (fileToUse) {
        const buffer = await fileToUse.arrayBuffer();
        // Determine correct MIME type based on the uploaded file's extension
        const lcFileName = (resumeFileName || fileToUse.name).toLowerCase();
        let mimeType: string;
        if (lcFileName.endsWith('.pdf')) {
          mimeType = 'application/pdf';
        } else if (lcFileName.endsWith('.docx')) {
          mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        } else if (lcFileName.endsWith('.png')) {
          mimeType = 'image/png';
        } else if (lcFileName.endsWith('.jpg') || lcFileName.endsWith('.jpeg')) {
          mimeType = 'image/jpeg';
        } else {
          mimeType = 'text/plain';
        }
        resumeInput.file = {
          buffer, 
          mimeType
        };
      }

      if (!resumeInput.file) {
        return NextResponse.json({
          error: 'Resume file is required for upload method'
        }, { status: 400 });
      }
    } else if (resumeInputMethod === 'scratch') {
      resumeInput.manual = {
        fullName: fullName || '',
        professionalTitle: professionalTitle || '',
        professionalSummary: professionalSummary || '',
        mostRecentJob: {
          title: jobTitle || '',
          company: company || '',
          achievements: achievements || ''
        },
        skills: skills || ''
      };
    } else {
      return NextResponse.json({
        error: 'Invalid resume input method'
      }, { status: 400 });
    }

    // Prepare job input
    const jobInput: JobInput = {};

    if (jobInputMethod === 'text') {
      if (!jobDescription) {
        return NextResponse.json({
          error: 'Job description is required for text method'
        }, { status: 400 });
      }
      jobInput.description = jobDescription;
    } else if (jobInputMethod === 'image') {
      if (!jobImage) {
        return NextResponse.json({
          error: 'Job image is required for image method'
        }, { status: 400 });
      }

      const buffer = await jobImage.arrayBuffer();
      jobInput.image = {
        buffer,
        mimeType: jobImage.type
      };
    } else {
      return NextResponse.json({
        error: 'Invalid job input method'
      }, { status: 400 });
    }

    // Validate templateId if provided
    if (templateId && typeof templateId !== 'string') {
      return NextResponse.json({
        error: 'Template ID must be a string'
      }, { status: 400 });
    }

    // Create database record with 'processing' status in resumes table
    const { data: insertData, error: insertError } = await supabase
      .from('resumes')
      .insert({
        user_id: userId ?? null,
        data: { resumeInput, jobInput, templateId, buildMethod: 'job-specific' }, // Store both inputs, templateId, and build method
        status: 'processing',
        template_id: templateId,
        tokens_deducted: false, // AI-powered resumes have tokens deducted during generation (25 tokens)
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (insertError || !insertData) {
      console.error('Supabase insert error:', insertError);
      return NextResponse.json({ error: 'Failed to create generation record' }, { status: 500 });
    }

    const id = insertData.id as string;

    // Prepare Edge Function request
    const edgeFunctionRequest: EdgeFunctionRequest = {
      resumeId: id,
      resumeInput,
      jobInput,
      templateId
    };

    // Prepare data for frontend to call edge function
    // Convert ArrayBuffers to Base64 strings for JSON serialization
    const edgeFunctionData = {
      resumeId: id,
      resumeInput: {
        ...resumeInput,
        file: resumeInput.file ? {
          ...resumeInput.file,
          buffer: resumeInput.file.buffer && resumeInput.file.buffer instanceof ArrayBuffer ?
            Buffer.from(resumeInput.file.buffer).toString('base64') :
            resumeInput.file.buffer
        } : undefined
      },
      jobInput: {
        ...jobInput,
        image: jobInput.image ? {
          ...jobInput.image,
          buffer: jobInput.image.buffer instanceof ArrayBuffer ?
            Buffer.from(jobInput.image.buffer).toString('base64') :
            jobInput.image.buffer
        } : undefined
      },
      templateId
    };

    // Track usage for initiation
    if (userId) {
      trackApiUsage(
        'resume-generation-initiated',
        'success',
        Date.now() - startTime,
        {
          resumeInputMethod,
          jobInputMethod,
          resumeId: id
        },
        userId
      );
    }

    // Return the data needed for frontend to call edge function
    return NextResponse.json({
      id,
      edgeFunctionData
    });
  } catch (error) {
    console.error('Resume generation request error:', error);

    captureApiError(String(error), {
      context: 'resume-generation-request',
      userId
    });
    
    return NextResponse.json({
      error: 'Failed to process resume generation request'
    }, { status: 500 });
  }
}

export const POST = createApiHandler(
  "resume-generate-start",
  handlePostRequest
);
